import { DisplayAvatar } from "@/components/DisplayAvatar";
import { motion } from "framer-motion";
import type { NavigateFunction } from "react-router-dom";

interface MessageToastProps {
    toastId: string;
    userData?: { username: string; avatar: string | null; [key: string]: unknown };
    onReply: (id: string, navigate: NavigateFunction) => void;
    navigate: NavigateFunction;
}

export const MessageToast = ({ toastId, userData, onReply, navigate }: MessageToastProps) => {
    return (
        <motion.div
            initial={{ opacity: 0, scale: 0.8, y: -50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, x: 100 }}
            className="relative overflow-hidden rounded-xl border border-blue-500/30 backdrop-blur-xl bg-gradient-to-br from-blue-500/20 via-blue-600/10 to-blue-700/20 shadow-2xl shadow-black/20 max-w-md"
            style={{
                background: `linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.6) 100%)`,
            }}
        >
            <div className="relative z-10 flex items-center gap-4 p-4">
                <DisplayAvatar className="size-10 rounded-full ring-2 ring-blue-400/30" src={userData} />

                <div className="flex-1">
                    <p className="text-blue-400 text-sm font-semibold">{userData?.username || "Unknown User"}</p>
                    <p className="text-white/80 text-xs mt-1">Just sent you a message!</p>
                </div>
                <button
                    className="px-3 py-1.5 bg-blue-500/20 text-blue-400 text-xs font-medium rounded-lg border border-blue-500/30"
                    onClick={() => onReply(toastId, navigate)}
                >
                    View
                </button>
            </div>
        </motion.div>
    );
};
