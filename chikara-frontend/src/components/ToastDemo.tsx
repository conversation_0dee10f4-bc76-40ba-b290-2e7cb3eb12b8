import React from "react";
import { toast } from "react-hot-toast";
import { motion } from "framer-motion";
import { CheckCircle, AlertCircle, Info, MessageCircle } from "lucide-react";
import { MessageToast } from "./MessageToast";

const ToastDemo = () => {
    const showSuccessToast = () => {
        toast.success("Mission completed successfully! You earned 100 XP and found a rare item.");
    };

    const showErrorToast = () => {
        toast.error("You were caught attacking another student and sent to jail!");
    };

    const showInfoToast = () => {
        toast("A staff member sent you a gift! Check your inventory.");
    };

    const showLoadingToast = () => {
        toast.loading("Traveling to new location...");
    };

    const showCustomMessageToast = () => {
        toast.custom((t) => (
            <MessageToast
                toastId={t.id}
                userData={{ username: "John Doe", avatar: "https://via.placeholder.com/150" }}
                onReply={() => {}}
                navigate={() => {}}
            />
        ));
    };

    return (
        <div className="p-8 space-y-4 bg-gray-900 min-h-screen">
            <h1 className="text-3xl font-bold text-white mb-8">Enhanced Toast Demo</h1>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={showSuccessToast}
                    className="flex items-center gap-3 p-4 bg-green-500/20 border border-green-500/30 rounded-xl text-green-400 hover:bg-green-500/30 transition-all duration-200"
                >
                    <CheckCircle size={24} />
                    <div className="text-left">
                        <p className="font-semibold">Success Toast</p>
                        <p className="text-sm text-green-300">Mission completed</p>
                    </div>
                </motion.button>

                <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={showErrorToast}
                    className="flex items-center gap-3 p-4 bg-red-500/20 border border-red-500/30 rounded-xl text-red-400 hover:bg-red-500/30 transition-all duration-200"
                >
                    <AlertCircle size={24} />
                    <div className="text-left">
                        <p className="font-semibold">Error Toast</p>
                        <p className="text-sm text-red-300">Jail notification</p>
                    </div>
                </motion.button>

                <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={showInfoToast}
                    className="flex items-center gap-3 p-4 bg-purple-500/20 border border-purple-500/30 rounded-xl text-purple-400 hover:bg-purple-500/30 transition-all duration-200"
                >
                    <Info size={24} />
                    <div className="text-left">
                        <p className="font-semibold">Info Toast</p>
                        <p className="text-sm text-purple-300">Admin gift</p>
                    </div>
                </motion.button>

                <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={showLoadingToast}
                    className="flex items-center gap-3 p-4 bg-blue-500/20 border border-blue-500/30 rounded-xl text-blue-400 hover:bg-blue-500/30 transition-all duration-200"
                >
                    <Info size={24} />
                    <div className="text-left">
                        <p className="font-semibold">Loading Toast</p>
                        <p className="text-sm text-blue-300">Travel notification</p>
                    </div>
                </motion.button>

                <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={showCustomMessageToast}
                    className="flex items-center gap-3 p-4 bg-indigo-500/20 border border-indigo-500/30 rounded-xl text-indigo-400 hover:bg-indigo-500/30 transition-all duration-200"
                >
                    <MessageCircle size={24} />
                    <div className="text-left">
                        <p className="font-semibold">Message Toast</p>
                        <p className="text-sm text-indigo-300">Custom message</p>
                    </div>
                </motion.button>
            </div>

            <div className="mt-8 p-6 bg-gray-800/50 rounded-xl border border-gray-700">
                <h2 className="text-xl font-semibold text-white mb-4">Enhanced Features</h2>
                <ul className="space-y-2 text-gray-300">
                    <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                        Smooth spring animations with blur effects
                    </li>
                    <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                        Animated text with word-by-word reveal
                    </li>
                    <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                        Shimmer effects and gradient backgrounds
                    </li>
                    <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                        Progress bars and interactive close buttons
                    </li>
                    <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-pink-400 rounded-full"></div>
                        Type-specific icons and color schemes
                    </li>
                    <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-indigo-400 rounded-full"></div>
                        Enhanced message toasts with avatars
                    </li>
                </ul>
            </div>
        </div>
    );
};

export default ToastDemo;
