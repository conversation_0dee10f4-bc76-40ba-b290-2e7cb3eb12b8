import React, { useState, useEffect } from "react";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { ToastBar, Toaster, toast } from "react-hot-toast";
import { X } from "lucide-react";
import { useLocation } from "react-router-dom";
import { SuccessIcon, ErrorIcon, LoadingIcon, InfoIcon } from "./ToastIcons";

// Helper function to get toast styling based on type
const getToastStyle = (type: string) => {
    switch (type) {
        case "success":
            return {
                icon: SuccessIcon,
                iconColor: "",
                gradient: "from-emerald-950 via-emerald-900 to-green-950",
                borderColor: "border-emerald-400/60",
                accentGradient: "from-emerald-400 to-green-400",
                glowColor: "shadow-emerald-500/40",
                overlayGradient: "from-emerald-500/15 via-transparent to-emerald-600/10",
            };
        case "error":
            return {
                icon: ErrorIcon,
                iconColor: "",
                gradient: "from-red-950 via-red-900 to-rose-950",
                borderColor: "border-red-400/60",
                accentGradient: "from-red-400 to-rose-400",
                glowColor: "shadow-red-500/40",
                overlayGradient: "from-red-500/15 via-transparent to-red-600/10",
            };
        case "loading":
            return {
                icon: LoadingIcon,
                iconColor: "",
                gradient: "from-blue-950 via-blue-900 to-indigo-950",
                borderColor: "border-blue-400/60",
                accentGradient: "from-blue-400 to-indigo-400",
                glowColor: "shadow-blue-500/40",
                overlayGradient: "from-blue-500/15 via-transparent to-blue-600/10",
            };
        default:
            return {
                icon: InfoIcon,
                iconColor: "",
                gradient: "from-violet-950 via-purple-900 to-indigo-950",
                borderColor: "border-violet-400/60",
                accentGradient: "from-violet-400 to-purple-400",
                glowColor: "shadow-violet-500/40",
                overlayGradient: "from-violet-500/15 via-transparent to-violet-600/10",
            };
    }
};

const ToastManager = () => {
    const isMobile = useCheckMobileScreen();
    const location = useLocation();
    const isChatPage = location.pathname === "/chat";
    const [windowWidth, setWindowWidth] = useState(window.innerWidth);

    // Handle window resize to update toast positioning
    useEffect(() => {
        const handleResize = () => setWindowWidth(window.innerWidth);
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    let containerStyle: React.CSSProperties = {};
    if (isMobile) {
        containerStyle = {
            top: 65,
        };
    } else {
        // Position toasts in the top right of the main content container
        // Account for:
        // - Navbar height (60px on regular, 80px on 2xl)
        // - Minimal space for potential PageBanner
        // - Chatbox width (21.5vw on xl, 25.51vw on 2xl screens) when visible
        // - Small padding from the edge
        const navbarHeight = windowWidth >= 1536 ? 80 : 60; // 2xl breakpoint
        containerStyle = {
            top: navbarHeight + 16, // Navbar + minimal padding to be in top corner of content
        };

        // If chatbox is visible (not on chat page), account for its width
        if (!isChatPage) {
            containerStyle.right = "calc(21.5vw + 1rem)"; // Chatbox width + padding for xl screens

            // For 2xl screens, adjust for larger chatbox width
            if (windowWidth >= 1536) {
                // 2xl breakpoint
                containerStyle.right = "calc(25.51vw + 1rem)";
            }
        } else {
            // On chat page, chatbox is hidden, so position closer to right edge
            containerStyle.right = "1rem";
        }
    }

    return (
        <Toaster
            position={isMobile ? "top-center" : "top-right"}
            containerStyle={containerStyle}
            toastOptions={{
                duration: 4000,
                style: {
                    background: "transparent",
                    border: "none",
                    boxShadow: "none",
                    padding: 0,
                    maxWidth: isMobile ? "20rem" : "24rem",
                },
            }}
        >
            {(t) => (
                <ToastBar toast={t}>
                    {({ message }) => {
                        const toastStyle = getToastStyle(t.type);
                        const IconComponent = toastStyle.icon;

                        return (
                            <div
                                className={`
                                    relative overflow-hidden rounded-2xl border-2 backdrop-blur-md
                                    bg-gradient-to-br ${toastStyle.gradient}
                                    ${toastStyle.borderColor} ${toastStyle.glowColor}
                                    shadow-2xl cursor-pointer group
                                    hover:scale-[1.03] hover:shadow-3xl transition-all duration-300 ease-out
                                    ${isMobile ? "max-w-sm mx-2" : "max-w-md"}
                                    ring-1 ring-white/10
                                `}
                                onClick={() => toast.dismiss(t.id)}
                            >
                                {/* Enhanced overlay for depth and color */}
                                <div
                                    className={`absolute inset-0 bg-gradient-to-br ${toastStyle.overlayGradient} pointer-events-none`}
                                />
                                <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/30 pointer-events-none" />
                                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-white/5 pointer-events-none" />

                                {/* Content */}
                                <div className="relative flex items-center gap-3 p-3">
                                    {/* Icon */}
                                    <div className={`flex-shrink-0 ${toastStyle.iconColor} drop-shadow-sm`}>
                                        <IconComponent size={18} className="" />
                                    </div>

                                    {/* Message */}
                                    <div className="flex-1 min-w-0">
                                        <div className="text-white font-display text-xs font-medium leading-snug tracking-normal drop-shadow-md">
                                            {message}
                                        </div>
                                    </div>

                                    {/* Close button */}
                                    <button
                                        className="flex-shrink-0 text-white/70 hover:text-white transition-all duration-200 p-1 rounded-md hover:bg-white/15 group-hover:opacity-100 opacity-80 hover:scale-110"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            toast.dismiss(t.id);
                                        }}
                                    >
                                        <X size={14} strokeWidth={1.5} />
                                    </button>
                                </div>

                                {/* Progress bar */}
                                <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/30 overflow-hidden rounded-b-2xl">
                                    <div
                                        className={`h-full bg-gradient-to-r ${toastStyle.accentGradient} transition-all duration-75 ease-linear shadow-lg`}
                                        style={{
                                            width: `${((4000 - (Date.now() - t.createdAt)) / 4000) * 100}%`,
                                            transition: t.visible ? "width 4000ms linear" : "none",
                                            boxShadow: `0 0 8px ${toastStyle.iconColor.replace("text-", "").replace("-300", "")}-500/50`,
                                        }}
                                    />
                                </div>
                            </div>
                        );
                    }}
                </ToastBar>
            )}
        </Toaster>
    );
};

export default ToastManager;
