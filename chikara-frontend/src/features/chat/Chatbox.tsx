import ErrorBoundary from "@/components/Layout/ErrorBoundary";
import Spinner from "@/components/Spinners/Spinner";
import useGetChatHistory from "@/features/chat/api/useGetChatHistory";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import useGameConfig from "@/hooks/useGameConfig";
import { cn } from "@/lib/utils";
import { Fragment, useEffect, useLayoutEffect, useRef, useState } from "react";
import { useLocation } from "react-router-dom";
import { useNormalStore, usePersistStore, useSessionStore, useSocketStore } from "../../app/store/stores";
import ChatMessage from "./components/ChatMessage";
import ChatTextAreaInput from "./components/ChatTextAreaInput";
import ChatTopPanel from "./components/ChatTopPanel";
import ConsoleMessage from "./components/ConsoleMessage";
import type { ChatMessage as ChatMessageType, ChatRoom } from "./types/chat";

interface ChatboxProps {
    isMainChat?: boolean;
    fullSize?: boolean;
    chatRoom?: ChatRoom["room"];
    hideHeader?: boolean;
    customHeight?: string | null;
}

const Chatbox = ({
    isMainChat = false,
    fullSize = false,
    chatRoom = { id: 1, name: "global" },
    hideHeader = false,
    customHeight = null,
}: ChatboxProps) => {
    const { isSocketConnected, socket } = useSocketStore();
    const [newMessagesBox, setNewMessagesBox] = useState<boolean>(false);
    const [focusChatMsgInput, setFocusChatMsgInput] = useState<boolean>(false);
    const [selectedReplyMessage, setSelectedReplyMessage] = useState<ChatMessageType | null>(null);
    const { muteChat, setMuteChat, hideGlobalChat } = usePersistStore();
    const { hideChat, setHideChat, mainChatRoom } = useSessionStore();

    const isChatHidden = hideChat || hideGlobalChat;

    const { resetUnreadChatMessages } = useNormalStore();
    const location = useLocation();
    const chatConfig = useGameConfig();

    let room: ChatRoom["room"];
    if (isMainChat) {
        room = mainChatRoom.room;
    } else {
        room = chatRoom;
    }

    const { data: currentUser, isLoading: userIsLoading } = useFetchCurrentUser();
    const { data: chatMessages, isLoading } = useGetChatHistory(
        {
            roomId: room.id,
            limit: 200,
        },
        {
            staleTime: Number.POSITIVE_INFINITY,
            select: (data: ChatMessageType[]) => [...data].reverse(),
        }
    );
    const isMobile = useCheckMobileScreen();

    const messagesEndRef = useRef<HTMLDivElement>(null);
    const messageWindowRef = useRef<HTMLDivElement>(null);

    const triggerScroll = (): void => {
        // scrollIntoViewIfNeeded ?
        messagesEndRef?.current?.scrollIntoView({ behavior: "smooth" });

        setNewMessagesBox(false);
    };

    const scrollToBottom = (always?: boolean): void => {
        if (!hideChat) {
            if (always) {
                setTimeout(() => triggerScroll(), 50);
                return;
            }
            if (messageWindowRef?.current?.scrollTop > -300) {
                setTimeout(() => triggerScroll(), 50);
                return;
            } else {
                const lastMessage = chatMessages[chatMessages?.length - 1];
                if (lastMessage?.userId !== currentUser?.id) {
                    setNewMessagesBox(true);
                }
            }
        }
    };

    useLayoutEffect(scrollToBottom, [chatMessages]);

    useEffect(() => {
        resetUnreadChatMessages();
        if (location.pathname === "/error") return null;
    }, []);

    if (isChatHidden) return null;

    return (
        <aside
            data-testid="chatbox-panel"
            className={cn(
                isMobile
                    ? "fixed flex h-[calc(100dvh-8.75rem)] w-full"
                    : `relative hidden max-h-full min-h-full min-w-[150px] max-w-[392px] xl:flex`,
                customHeight ? customHeight : "",
                fullSize ? "w-full rounded-b-lg md:max-w-full" : "w-[21.5vw] 2xl:w-[25.51vw]",
                "globalChatBoxShadow shrink-0 flex-col border-gray-400 bg-[#ebedf0] px-3 pt-1 pb-2 md:z-20 md:mb-0 md:rounded-tl-xl md:border-t md:border-l md:px-3 md:py-2 dark:border-[#292c3e] dark:bg-[#191924]"
            )}
        >
            {chatConfig?.CHAT_DISABLED ? (
                <div className="flex h-full flex-col dark:text-slate-200">
                    <div className="m-auto text-center">
                        <h2 className="text-xl">Chat currently Disabled</h2>
                        <p>Please return later.</p>
                    </div>
                </div>
            ) : (
                <ErrorBoundary>
                    {hideHeader ? null : (
                        <ChatTopPanel
                            muteChat={muteChat}
                            setMuteChat={setMuteChat}
                            currentUser={currentUser}
                            socket={socket}
                            setHideChat={setHideChat}
                            chatRoom={isMainChat ? mainChatRoom : { room }}
                            fullSize={fullSize}
                        />
                    )}

                    <div
                        ref={messageWindowRef}
                        className="noscrollbar flex h-full flex-col-reverse overflow-y-auto overflow-x-hidden"
                    >
                        {!isLoading && !userIsLoading && isSocketConnected ? (
                            <div>
                                {chatMessages?.map((msg) => {
                                    return (
                                        <Fragment key={msg.id}>
                                            {msg.announcementType ? (
                                                <ConsoleMessage msg={msg} type={msg.announcementType} />
                                            ) : (
                                                <ChatMessage
                                                    msg={msg}
                                                    currentUser={currentUser}
                                                    setSelectedReplyMessage={setSelectedReplyMessage}
                                                    setFocusChatMsgInput={setFocusChatMsgInput}
                                                />
                                            )}
                                        </Fragment>
                                    );
                                })}
                                {newMessagesBox && (
                                    <div
                                        className="-translate-x-1/2 absolute bottom-24 left-1/2 z-20 cursor-pointer rounded-md border border-black bg-blue-600 px-3 py-1 text-lg text-stroke-s-sm dark:text-slate-200"
                                        onClick={() => scrollToBottom(true)}
                                    >
                                        New Messages
                                    </div>
                                )}
                                <div ref={messagesEndRef} />
                            </div>
                        ) : (
                            <Spinner center />
                        )}
                    </div>
                    {chatConfig?.CHAT_MESSAGE_SENDING_DISABLED ? (
                        <div className="m-2 flex flex-col rounded-md border border-slate-400 p-2 dark:text-slate-200">
                            <div className="m-auto text-center">
                                <h2 className="text-xl">Chat currently Disabled</h2>
                                <p>Please return later.</p>
                            </div>
                        </div>
                    ) : (
                        <ChatTextAreaInput
                            chatBannedUntil={currentUser?.chatBannedUntil}
                            socket={socket}
                            scrollToBottom={scrollToBottom}
                            chatRoom={room}
                            currentUser={currentUser}
                            selectedReplyMessage={selectedReplyMessage}
                            setSelectedReplyMessage={setSelectedReplyMessage}
                            focusChatMsgInput={focusChatMsgInput}
                            setFocusChatMsgInput={setFocusChatMsgInput}
                        />
                    )}
                </ErrorBoundary>
            )}
        </aside>
    );
};

export default Chatbox;
