import type { QuestWithProgress } from "@/types/quest";
import { useMemo, useState } from "react";

// Sort options
export type QuestSortOption = "level" | "name" | "quest_giver" | "Yen_reward" | "XP_reward" | "REP_reward";
export type SortDirection = "asc" | "desc";

// Filter options
export type QuestFilterOption = {
    status?: "available" | "in_progress" | "complete" | "ready_to_complete" | "all";
    minLevel?: number;
    maxLevel?: number;
    questGiverId?: number;
    hasItemRewards?: boolean;
    hasTalentPointRewards?: boolean;
    location?: "church" | "shrine" | "mall" | "alley" | "school" | "sewers" | "themepark" | "any";
    isStoryQuest?: boolean;
    chapterId?: number;
};

// Get quest status in a more reusable way
export const getQuestStatus = (
    quest: QuestWithProgress
): "available" | "in_progress" | "complete" | "ready_to_complete" => {
    if (!quest.quest_progress || quest.quest_progress.length === 0) {
        return "available";
    }
    return quest.quest_progress[0].questStatus as "available" | "in_progress" | "complete" | "ready_to_complete";
};

export default function useQuestSortAndFilter(quests: QuestWithProgress[] = []) {
    // Default sort option is level
    const [sortOption, setSortOption] = useState<QuestSortOption>("level");
    const [sortDirection, setSortDirection] = useState<SortDirection>("asc");
    const [filterOptions, setFilterOptions] = useState<QuestFilterOption>({
        status: "all",
    });

    // Filter and sort quests using useMemo for performance
    const filteredAndSortedQuests = useMemo(() => {
        if (!quests || quests.length === 0) return [];

        // First apply filters
        const result = quests.filter((quest) => {
            // Filter by status
            if (filterOptions.status && filterOptions.status !== "all") {
                const status = getQuestStatus(quest);
                if (status !== filterOptions.status) return false;
            }

            // Filter by level requirements
            if (filterOptions.minLevel !== undefined && quest.levelReq < filterOptions.minLevel) return false;
            if (filterOptions.maxLevel !== undefined && quest.levelReq > filterOptions.maxLevel) return false;

            // Filter by quest giver
            if (filterOptions.questGiverId !== undefined && quest.shopId !== filterOptions.questGiverId) return false;

            // Filter by item rewards
            if (filterOptions.hasItemRewards === true) {
                const hasItemReward = quest.quest_reward.some((reward) => reward.rewardType === "ITEM");
                if (!hasItemReward) return false;
            }

            // Filter by talent point rewards
            if (filterOptions.hasTalentPointRewards === true) {
                const hasTalentPointReward = quest.talentPointReward > 0;
                if (!hasTalentPointReward) return false;
            }

            // Filter by location
            if (filterOptions.location !== undefined && filterOptions.location !== "any") {
                const matchesLocation = quest.quest_objective.some(
                    (objective) => objective.location?.toLowerCase() === filterOptions.location
                );
                if (!matchesLocation) return false;
            }

            // Filter by story quest status
            if (filterOptions.isStoryQuest !== undefined) {
                if (quest.isStoryQuest !== filterOptions.isStoryQuest) return false;
            }

            // Filter by chapter ID
            if (filterOptions.chapterId !== undefined) {
                if (quest.chapterId !== filterOptions.chapterId) return false;
            }

            return true;
        });

        // Then sort the filtered results
        return result.sort((a, b) => {
            let comparison = 0;

            switch (sortOption) {
                case "level":
                    comparison = a.levelReq - b.levelReq;
                    break;

                case "name":
                    comparison = a.name.localeCompare(b.name);
                    break;

                case "quest_giver":
                    comparison = (a.shopId || 0) - (b.shopId || 0);
                    break;

                case "Yen_reward": {
                    const aCash = a.cashReward || 0;
                    const bCash = b.cashReward || 0;
                    comparison = bCash - aCash;
                    break;
                }

                case "XP_reward": {
                    const aXP = a.xpReward || 0;
                    const bXP = b.xpReward || 0;
                    comparison = bXP - aXP;
                    break;
                }

                case "REP_reward": {
                    const aREP = a.repReward || 0;
                    const bREP = b.repReward || 0;
                    comparison = bREP - aREP;
                    break;
                }
            }

            return sortDirection === "asc" ? comparison : -comparison;
        });
    }, [quests, sortOption, sortDirection, filterOptions]);

    return {
        filteredAndSortedQuests,
        sortOption,
        setSortOption,
        sortDirection,
        setSortDirection,
        filterOptions,
        setFilterOptions,
    };
}
