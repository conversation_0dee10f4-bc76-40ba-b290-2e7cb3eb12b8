import type { QuestFilterOption } from "@/features/tasks/hooks/useQuestSortAndFilter";
import { cn } from "@/lib/utils";

interface StatusTabProps {
    name: string;
    count: number;
    isActive: boolean;
    onClick: () => void;
    color: "yellow" | "blue" | "green";
}

function StatusTab({ name, count, isActive, onClick, color }: StatusTabProps) {
    return (
        <button
            className={cn(
                "flex-1 font-display px-3 py-2 rounded-lg text-sm transition-colors flex items-center justify-center gap-1.5",
                isActive
                    ? color === "yellow"
                        ? "bg-yellow-900/30 text-yellow-300 border border-yellow-900/50"
                        : color === "blue"
                          ? "bg-blue-900/30 text-blue-300 border border-blue-900/50"
                          : "bg-green-900/30 text-green-300 border border-green-900/50"
                    : "bg-gray-800/30 text-gray-400 border border-gray-800 hover:text-gray-300"
            )}
            onClick={onClick}
        >
            {name}
            <span
                className={cn(
                    "px-1.5 py-0.5 rounded-full text-xs",
                    isActive
                        ? color === "yellow"
                            ? "bg-yellow-900/50 text-yellow-300"
                            : color === "blue"
                              ? "bg-blue-900/50 text-blue-300"
                              : "bg-green-900/50 text-green-300"
                        : "bg-gray-800 text-gray-400"
                )}
            >
                {count}
            </span>
        </button>
    );
}

type QuestTab = "current" | "completed" | "story";

interface TaskStatusTabsProps {
    activeTab: QuestTab;
    setActiveTab: (_activeTab: QuestTab) => void;
    currentQuestLength: number;
    completedQuestLength: number;
    storyQuestLength: number;
    setFilterOptions: (_options: QuestFilterOption) => void;
}

export default function TaskStatusTabs({
    activeTab,
    setActiveTab,
    currentQuestLength,
    completedQuestLength,
    storyQuestLength,
    setFilterOptions,
}: TaskStatusTabsProps) {
    return (
        <div className="flex space-x-1">
            <StatusTab
                name="Main Story"
                count={storyQuestLength}
                isActive={activeTab === "story"}
                color="blue"
                onClick={() => {
                    setActiveTab("story");
                    setFilterOptions({ status: "all" });
                }}
            />

            <StatusTab
                name="Current"
                count={currentQuestLength}
                isActive={activeTab === "current"}
                color="yellow"
                onClick={() => {
                    setActiveTab("current");
                    setFilterOptions({ status: "all" });
                }}
            />

            <StatusTab
                name="Completed"
                count={completedQuestLength}
                isActive={activeTab === "completed"}
                color="green"
                onClick={() => {
                    setActiveTab("completed");
                    setFilterOptions({ status: "all" });
                }}
            />
        </div>
    );
}
