import { cn } from "@/lib/utils";
import { SlidersHorizontal } from "lucide-react";
/* eslint-disable no-unused-vars */
import { useMemo } from "react";
import type { QuestFilterOption, QuestSortOption, SortDirection } from "../hooks/useQuestSortAndFilter";
import type { QuestGiver } from "@/types/quest";

interface QuestSortAndFilterControlsProps {
    sortOption: QuestSortOption;
    setSortOption: (option: QuestSortOption) => void;
    sortDirection: SortDirection;
    setSortDirection: (direction: SortDirection) => void;
    filterOptions: QuestFilterOption;
    setFilterOptions: (options: QuestFilterOption) => void;
    questGivers: QuestGiver[];
    showFilters: boolean;
    setShowFilters: (show: boolean) => void;
    activeTab: string;
}

export default function QuestSortAndFilterControls({
    sortOption,
    setSortOption,
    sortDirection,
    setSortDirection,
    filterOptions,
    setFilterOptions,
    questGivers,
    showFilters,
    setShowFilters,
    activeTab,
}: QuestSortAndFilterControlsProps) {
    // Track if any filters are active
    const hasActiveFilters = useMemo(() => {
        return (
            (filterOptions.status && filterOptions.status !== "all") ||
            filterOptions.minLevel !== undefined ||
            filterOptions.maxLevel !== undefined ||
            filterOptions.questGiverId !== undefined ||
            filterOptions.hasItemRewards === true ||
            filterOptions.hasTalentPointRewards === true ||
            (filterOptions.location !== undefined && filterOptions.location !== "any")
        );
    }, [filterOptions]);

    // Clear all filters
    const clearAllFilters = () => {
        setFilterOptions({
            status: "all",
        });
    };

    // Toggle sort direction
    const setAscOrDesc = () => {
        setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    };

    return (
        <div className="space-y-2 absolute right-2">
            {/* Filter Button */}
            <div className="flex items-center justify-end gap-4">
                <div
                    className="cursor-pointer text-xs text-gray-400 text-stroke-0 flex items-center gap-1"
                    onClick={() => setAscOrDesc()}
                >
                    <span className="text-gray-300 font-bold uppercase font-mono">{sortOption.replace("_", " ")}</span>
                    {/* <ArrowDownUp size={14} /> */}
                    <span className="text-xs text-gray-300">{sortDirection === "asc" ? "↑" : "↓"}</span>
                </div>
                <button
                    className={cn(
                        "px-3 py-2 text-sm rounded-md flex items-center gap-1.5",
                        hasActiveFilters
                            ? "bg-indigo-900/50 text-indigo-300 border border-indigo-900/70"
                            : "bg-gray-800/60 text-gray-400 border border-gray-800"
                    )}
                    onClick={() => setShowFilters(!showFilters)}
                >
                    <SlidersHorizontal className="size-3" />
                    {hasActiveFilters && <span className="size-2 rounded-full bg-indigo-400"></span>}
                </button>
            </div>

            {/* Filter Panel (Collapsible) */}
            {showFilters && (
                <div className="bg-gray-800/70 border border-gray-700 rounded-lg p-3 space-y-3">
                    {/* Sort Options */}
                    <div className="space-y-1">
                        <label className="text-xs text-gray-400">Sort By</label>
                        <select
                            value={sortOption}
                            className="w-full bg-gray-900 border border-gray-700 rounded-md px-2 py-1 text-xs text-gray-300"
                            onChange={(e) => setSortOption(e.target.value as QuestSortOption)}
                        >
                            <option value="level">Level</option>
                            <option value="name">Name</option>
                            <option value="Yen_reward">Yen Reward</option>
                            <option value="XP_reward">XP Reward</option>
                            <option value="REP_reward">REP Reward</option>
                        </select>
                    </div>

                    <div className="border-t border-gray-700/50 pt-3">
                        <h3 className="text-sm text-gray-300 font-medium mb-2">Filters</h3>
                        <div className="grid grid-cols-2 gap-2">
                            {/* Status Filter */}
                            <div className="space-y-1">
                                <label className="text-xs text-gray-400">Status</label>
                                <select
                                    disabled={activeTab === "complete"}
                                    value={filterOptions.status || "all"}
                                    className="w-full bg-gray-900 border border-gray-700 rounded-md px-2 py-1 text-xs text-gray-300"
                                    onChange={(e) =>
                                        setFilterOptions({
                                            ...filterOptions,
                                            status: e.target.value as
                                                | "available"
                                                | "in_progress"
                                                | "complete"
                                                | "ready_to_complete"
                                                | "all",
                                        })
                                    }
                                >
                                    <option value="all">
                                        {activeTab === "complete" ? "Completed" : "All Statuses"}
                                    </option>
                                    <option value="available">New</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="ready_to_complete">Ready to turn in</option>
                                </select>
                            </div>

                            {/* Quest Giver Filter */}
                            <div className="space-y-1">
                                <label className="text-xs text-gray-400">Quest Giver</label>
                                <select
                                    value={filterOptions.questGiverId || ""}
                                    className="w-full bg-gray-900 border border-gray-700 rounded-md px-2 py-1 text-xs text-gray-300"
                                    onChange={(e) =>
                                        setFilterOptions({
                                            ...filterOptions,
                                            questGiverId: e.target.value ? Number.parseInt(e.target.value) : undefined,
                                        })
                                    }
                                >
                                    <option value="">All Givers</option>
                                    {/* TODO: Add isQuestGiver field to shop models for filtering */}
                                    {questGivers &&
                                        questGivers
                                            .filter((giver) => giver.shopType !== "gang")
                                            .map((giver) => (
                                                <option key={giver.id} value={giver.id}>
                                                    {giver.name}
                                                </option>
                                            ))}
                                </select>
                            </div>
                        </div>

                        {/* Location Filter */}
                        <div className="space-y-1 mt-3">
                            <label className="text-xs text-gray-400">Location</label>
                            <select
                                value={filterOptions.location || "any"}
                                className="w-full bg-gray-900 border border-gray-700 rounded-md px-2 py-1 text-xs text-gray-300"
                                onChange={(e) =>
                                    setFilterOptions({
                                        ...filterOptions,
                                        location: e.target.value as
                                            | "church"
                                            | "shrine"
                                            | "mall"
                                            | "alley"
                                            | "school"
                                            | "sewers"
                                            | "themepark"
                                            | "any",
                                    })
                                }
                            >
                                <option value="any">Any Location</option>
                                <option value="church">Church</option>
                                <option value="shrine">Shrine</option>
                                <option value="mall">Mall</option>
                                <option value="alley">Alley</option>
                                <option value="school">School</option>
                                <option value="sewers">Sewers</option>
                            </select>
                        </div>

                        {/* Level Range */}
                        <div className="space-y-1 mt-3">
                            <div className="flex items-center justify-between">
                                <label className="text-xs text-gray-400">Level Range</label>
                                {(filterOptions.minLevel !== undefined || filterOptions.maxLevel !== undefined) && (
                                    <button
                                        className="text-xs text-indigo-400 hover:text-indigo-300"
                                        onClick={() =>
                                            setFilterOptions({
                                                ...filterOptions,
                                                minLevel: undefined,
                                                maxLevel: undefined,
                                            })
                                        }
                                    >
                                        Reset
                                    </button>
                                )}
                            </div>
                            <div className="flex gap-2">
                                <input
                                    type="number"
                                    placeholder="Min"
                                    value={filterOptions.minLevel || ""}
                                    min="1"
                                    className="w-full bg-gray-900 border border-gray-700 rounded-md px-2 py-1 text-xs text-gray-300"
                                    onChange={(e) =>
                                        setFilterOptions({
                                            ...filterOptions,
                                            minLevel: e.target.value ? Number.parseInt(e.target.value) : undefined,
                                        })
                                    }
                                />
                                <span className="text-gray-400 self-center">-</span>
                                <input
                                    type="number"
                                    placeholder="Max"
                                    value={filterOptions.maxLevel || ""}
                                    min="1"
                                    className="w-full bg-gray-900 border border-gray-700 rounded-md px-2 py-1 text-xs text-gray-300"
                                    onChange={(e) =>
                                        setFilterOptions({
                                            ...filterOptions,
                                            maxLevel: e.target.value ? Number.parseInt(e.target.value) : undefined,
                                        })
                                    }
                                />
                            </div>
                        </div>

                        {/* Checkboxes */}
                        <div className="flex gap-6 mt-3">
                            <label className="flex items-center gap-1.5 cursor-pointer">
                                <input
                                    type="checkbox"
                                    checked={filterOptions.hasItemRewards === true}
                                    className="rounded text-indigo-500 focus:ring-indigo-500 border-gray-700 bg-gray-900"
                                    onChange={(e) =>
                                        setFilterOptions({
                                            ...filterOptions,
                                            hasItemRewards: e.target.checked || undefined,
                                        })
                                    }
                                />
                                <span className="text-xs text-gray-300">Has Item Rewards</span>
                            </label>
                            <label className="flex items-center gap-1.5 cursor-pointer">
                                <input
                                    type="checkbox"
                                    checked={filterOptions.hasTalentPointRewards === true}
                                    className="rounded text-indigo-500 focus:ring-indigo-500 border-gray-700 bg-gray-900"
                                    onChange={(e) =>
                                        setFilterOptions({
                                            ...filterOptions,
                                            hasTalentPointRewards: e.target.checked || undefined,
                                        })
                                    }
                                />
                                <span className="text-xs text-gray-300">Has Talent Point Rewards</span>
                            </label>
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end gap-2 pt-2 border-t border-gray-700/50 mt-2">
                        <button
                            className="px-3 py-1 text-xs rounded-md bg-gray-900/70 text-gray-300 border border-gray-700 hover:bg-gray-900"
                            onClick={() => setShowFilters(false)}
                        >
                            Close
                        </button>

                        {hasActiveFilters && (
                            <button
                                className="px-3 py-1 text-xs rounded-md bg-red-900/30 text-red-300 border border-red-900/50 hover:bg-red-900/50"
                                onClick={clearAllFilters}
                            >
                                Clear Filters
                            </button>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
}
