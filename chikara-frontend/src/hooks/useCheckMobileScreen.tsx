import { useEffect, useState } from "react";

const useCheckMobileScreen = (returnWidth: boolean = false): boolean | number => {
    const [width, setWidth] = useState<number>(window.innerWidth);

    const handleWindowSizeChange = (): void => {
        setWidth(window.innerWidth);
    };

    useEffect(() => {
        window.addEventListener("resize", handleWindowSizeChange);
        return () => {
            window.removeEventListener("resize", handleWindowSizeChange);
        };
    }, []);

    if (returnWidth) return width;
    return width <= 768;
};

// Additional hook for more granular screen size detection
// export const useScreenSize = () => {
//     const [width, setWidth] = useState<number>(window.innerWidth);

//     const handleWindowSizeChange = (): void => {
//         setWidth(window.innerWidth);
//     };

//     useEffect(() => {
//         window.addEventListener("resize", handleWindowSizeChange);
//         return () => {
//             window.removeEventListener("resize", handleWindowSizeChange);
//         };
//     }, []);

//     return {
//         width,
//         isXs: width <= 360, // Extra small phones
//         isSm: width <= 480, // Small phones
//         isMd: width <= 768, // Tablets/large phones
//         isLg: width <= 1024, // Small laptops
//         isXl: width <= 1280, // Laptops
//         is2Xl: width <= 1536, // Large screens
//     };
// };

export default useCheckMobileScreen;
