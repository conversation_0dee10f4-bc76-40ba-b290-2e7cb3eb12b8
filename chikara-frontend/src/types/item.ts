import type { AppRouterClient } from "@/lib/orpc";

export type InventoryItem = Awaited<ReturnType<AppRouterClient["user"]["getInventory"]>>[number];
export type Item = InventoryItem["item"];
export type ItemType = NonNullable<Item>["itemType"];
export type ItemRarity = NonNullable<Item>["rarity"];

export type EquippedItems = Awaited<ReturnType<AppRouterClient["user"]["getEquippedItems"]>>;
export type EquippedItem = EquippedItems["chest"];

export interface ItemEffect {
    effectKey: string; // e.g. "lifesteal", "strength"
    effectModifier: "multiply" | "add" | "subtract" | "divide" | "set";
    effectValue?: number; // e.g. 10
    effectTier?: string;
    effectGroup?: string; // e.g. "treatment"
    description?: string; // User-facing description, e.g., "Increases Strength by 5."
}

// Enums used in the item interface
export enum ItemTypes {
    weapon = "weapon",
    ranged = "ranged",
    head = "head",
    chest = "chest",
    hands = "hands",
    legs = "legs",
    feet = "feet",
    finger = "finger",
    offhand = "offhand",
    shield = "shield",
    consumable = "consumable",
    crafting = "crafting",
    junk = "junk",
    quest = "quest",
    special = "special",
    recipe = "recipe",
    upgrade = "upgrade",
    pet = "pet",
    pet_food = "pet_food",
}

export enum ItemRarities {
    novice = "novice",
    standard = "standard",
    enhanced = "enhanced",
    specialist = "specialist",
    military = "military",
    legendary = "legendary",
}

export enum ItemQuality {
    shoddy = "shoddy",
    normal = "normal",
    fine = "fine",
    excellent = "excellent",
    superior = "superior",
    perfect = "perfect",
    masterwork = "masterwork",
}
